import React, { useState, useEffect, useContext, useRef } from 'react';
import usePageVisibility from '../../hooks/usePageVisibility';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import { useLoadingMonitor } from '../../utils/loading';
import { useDataSync } from '../../contexts/DataSyncContext';

// Import wizard steps
import StudioSelectionStep from '../../components/project/wizard/StudioSelectionStep';
import ProjectBasics from '../../components/project/wizard/ProjectBasics';
import TeamContributors from '../../components/project/wizard/TeamContributors';
import RoyaltyModel from '../../components/project/wizard/RoyaltyModel';
import RevenueTranches from '../../components/project/wizard/RevenueTranchesEnhanced';
import ContributionTracking from '../../components/project/wizard/ContributionTracking';
import Milestones from '../../components/project/wizard/Milestones';
import ReviewAgreement from '../../components/project/wizard/ReviewAgreement';

// Import wizard navigation
import WizardNavigation from '../../components/project/wizard/WizardNavigation';

// Import HeroUI components
import { Card, CardBody, Button } from '../../components/ui/heroui';

// Import the new ProjectSetupWizard (placeholder for now)
// import ProjectSetupWizard from '../../components/venture/VentureSetupWizard';

const ProjectWizard = () => {
  const { currentUser } = useContext(UserContext);
  const { triggerProjectSync } = useDataSync();
  const navigate = useNavigate();
  const location = useLocation();
  const { id, step } = useParams(); // For editing existing projects and step routing

  // Check if we're coming from the edit route
  const isEditRoute = location.pathname.includes('/edit');

  // Extract project ID from URL if we're on the edit route
  const projectIdFromPath = isEditRoute ? location.pathname.split('/project/')[1].split('/edit')[0] : id;

  // Check if we're creating a new project
  const isCreateRoute = location.pathname.includes('/project/create') ||
                       (location.pathname === '/project/wizard' && !id && !projectIdFromPath);



  // Parse step from URL parameter
  const urlStep = step ? parseInt(step, 10) : null;
  const initialStep = urlStep && urlStep >= 1 && urlStep <= 7 ? urlStep : 1;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState([]);
  // Enhanced Project Wizard is now the default and only mode
  const isPageVisible = usePageVisibility();

  // Studio selection state
  const [userStudios, setUserStudios] = useState([]);
  const [showStudioCreation, setShowStudioCreation] = useState(false);
  const [studioCheckComplete, setStudioCheckComplete] = useState(false);
  const fetchInProgress = useRef(false);

  // Initialize loading monitor
  const loadingMonitor = useLoadingMonitor('ProjectWizard');

  // Check for pre-populated data from onboarding bridge
  const getInitialProjectData = () => {
    const prePopulated = localStorage.getItem('wizardPrePopulate');
    if (prePopulated) {
      localStorage.removeItem('wizardPrePopulate'); // Use once
      return { ...JSON.parse(prePopulated) };
    }
    return {};
  };

  const [projectData, setProjectData] = useState({
    // Step 1: Project Basics
    name: '',
    description: '',
    project_type: '',
    start_date: new Date(), // Default to current date
    launch_date: null,
    estimated_duration: 6, // Default to 6 months
    thumbnail_url: '',
    is_public: true,
    team_id: null, // Studio assignment
    ...getInitialProjectData(), // Apply pre-populated data

    // Company Information
    company_name: '',
    company_address: '',
    company_state: '',
    company_county: '',
    company_city: '',
    contact_email: '',
    signer_name: '',
    signer_title: '',

    // Project-Specific Fields
    engine: '',
    platforms: '',
    genre: '',
    technology_stack: '',
    distribution_platforms: '',

    // Step 2: Team & Contributors
    contributors: [],

    // Step 3: Royalty Model
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 33.33,
        hours_weight: 33.33,
        difficulty_weight: 33.34
      },
      is_pre_expense: true,
      min_payout: 10000, // $100.00
      max_payout: 1000000, // $10,000.00
      revenue_share: 50 // 50%
    },

    // Step 4: Revenue Tranches
    revenue_tranches: [{
      name: 'Initial Release',
      description: '',
      start_date: null,
      end_date: null,
      revenue_sources: [],
      platform_fee_config: {
        apply_before: true,
        percentage: 5
      },
      distribution_thresholds: {
        minimum_revenue: 0,
        maximum_payout: null,
        per_contributor_minimum: 0,
        per_contributor_maximum: null
      },
      rollover_config: 'none'
    }],

    // Step 5: Contribution Tracking
    contribution_tracking: {
      categories: [],
      difficulty_scale: [1, 2, 3, 4, 5],
      task_templates: [],
      integrations: []
    },

    // Step 6: Milestones
    milestones: [],

    // Step 7: Agreement
    agreement_template: null
  });

  // Fetch project data if editing
  useEffect(() => {
    // Skip fetching if page is not visible
    if (!isPageVisible) {
      return;
    }

    // Prevent multiple concurrent fetches
    if (fetchInProgress.current) {
      return;
    }

    const fetchProject = async () => {
      fetchInProgress.current = true;
      const effectiveId = projectIdFromPath || id;

      // Skip fetching if this is a new project creation
      if (!effectiveId || effectiveId === 'new' || isCreateRoute) {
        setLoading(false);
        fetchInProgress.current = false;
        return;
      }

      // Start loading operation
      const loadingId = loadingMonitor.startLoading('fetchProject', `Loading project ${effectiveId}`);

      try {
        // Fetch project with retry logic
        let project = null;
        let projectError = null;
        let retryCount = 0;
        const maxRetries = 3;

        while (!project && retryCount < maxRetries) {
          try {
            const { data, error } = await supabase
              .from('projects')
              .select('*')
              .eq('id', effectiveId)
              .single();

            if (error) {
              projectError = error;
              throw error;
            }

            project = data;
          } catch (err) {
            console.warn(`Attempt ${retryCount + 1} failed:`, err);
            retryCount++;
            if (retryCount < maxRetries) {
              // Wait before retrying (exponential backoff)
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }

        if (!project) {
          throw projectError || new Error('Failed to fetch project after multiple attempts');
        }

        // Fetch contributors
        const { data: contributors, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', effectiveId);

        if (contributorsError) throw contributorsError;

        // Fetch royalty model
        const { data: royaltyModel, error: royaltyModelError } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', effectiveId)
          .maybeSingle();

        if (royaltyModelError && royaltyModelError.code !== 'PGRST116') throw royaltyModelError;

        // Fetch revenue tranches
        const { data: revenueTranches, error: revenueTranchesError } = await supabase
          .from('revenue_tranches')
          .select('*')
          .eq('project_id', effectiveId);

        if (revenueTranchesError) throw revenueTranchesError;

        // Fetch milestones
        const { data: milestones, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', effectiveId);

        if (milestonesError) throw milestonesError;

        // Update project data
        setProjectData({
          name: project.title || '',
          description: project.description || '',
          project_type: project.project_type || '',
          launch_date: project.launch_date || null,
          thumbnail_url: project.thumbnail_url || '',
          is_public: project.is_public !== undefined ? project.is_public : true,

          // Company Information
          company_name: project.company_name || '',
          company_address: project.company_address || '',
          company_state: project.company_state || '',
          company_county: project.company_county || '',

          // Project-Specific Fields
          engine: project.engine || '',
          platforms: project.platforms || '',
          genre: project.genre || '',
          technology_stack: project.technology_stack || '',
          distribution_platforms: project.distribution_platforms || '',
          contributors: contributors || [],
          royalty_model: royaltyModel || {
            model_type: 'custom',
            model_schema: 'cog',
            configuration: {
              tasks_weight: 33.33,
              hours_weight: 33.33,
              difficulty_weight: 33.34
            },
            is_pre_expense: true,
            min_payout: 10000, // $100.00
            max_payout: 1000000, // $10,000.00
            revenue_share: 50 // 50%
          },
          revenue_tranches: revenueTranches.length > 0 ? revenueTranches : [{
            name: 'Initial Release',
            description: '',
            start_date: null,
            end_date: null,
            revenue_sources: [],
            platform_fee_config: {
              apply_before: true,
              percentage: 5
            },
            distribution_thresholds: {
              minimum_revenue: 0,
              maximum_payout: null,
              per_contributor_minimum: 0,
              per_contributor_maximum: null
            },
            rollover_config: 'none'
          }],
          contribution_tracking: project.contribution_tracking_config || {
            categories: [],
            difficulty_scale: [1, 2, 3, 4, 5],
            task_templates: [],
            integrations: []
          },
          milestones: milestones || [],
          agreement_template: null
        });

        // Set wizard progress - prioritize URL step if provided
        if (project.wizard_progress) {
          const dbStep = project.wizard_progress.current_step || 1;
          const effectiveStep = urlStep || dbStep;
          setCurrentStep(effectiveStep);
          setCompletedSteps(project.wizard_progress.completed_steps || []);

          // If URL step differs from DB step, update URL to match DB
          if (!urlStep && dbStep !== initialStep) {
            const projectId = projectIdFromPath || id;
            if (projectId) {
              const basePath = isEditRoute ? `/project/${projectId}/edit` : `/project/wizard/${projectId}`;
              navigate(`${basePath}/step/${dbStep}`, { replace: true });
            }
          }
        }
      } catch (error) {
        console.error('Error fetching project:', error);
        toast.error('Failed to load project data. Redirecting to home page.');
        // End loading operation with error
        loadingMonitor.endLoading(loadingId, `error: ${error.message}`);
        // Short delay before navigating to give the user time to see the error message
        setTimeout(() => navigate('/'), 1500);
      } finally {
        setLoading(false);
        fetchInProgress.current = false;
        // Make sure loading operation is ended in case of success
        if (loadingMonitor.isLoading()) {
          loadingMonitor.endLoading(loadingId, 'success');
        }
      }
    };

    if (currentUser) {
      fetchProject();
    } else {
      setLoading(false);
    }
  }, [id, currentUser, navigate, isPageVisible, isCreateRoute]);

  // Fetch user studios for selection
  const fetchUserStudios = async () => {
    if (!currentUser) return;

    try {
      const { data: studios, error } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          description,
          studio_type,
          industry,
          team_members!inner(
            user_id,
            role,
            status
          )
        `)
        .eq('team_members.user_id', currentUser.id)
        .eq('team_members.status', 'active');

      if (error) throw error;

      setUserStudios(studios || []);
      setStudioCheckComplete(true);

      // If user has no studios, show studio creation
      if (!studios || studios.length === 0) {
        setShowStudioCreation(true);
      }

    } catch (error) {
      console.error('Error fetching user studios:', error);
      toast.error('Failed to load studios');
      setStudioCheckComplete(true);
    }
  };

  // Fetch studios when user is available
  useEffect(() => {
    if (currentUser && !studioCheckComplete) {
      fetchUserStudios();
    }
  }, [currentUser, studioCheckComplete]);

  // Save project data
  const saveProject = async (moveToNext = true) => {
    if (!currentUser) {
      toast.error('You must be logged in to create a project');
      return false;
    }

    // Check if page is visible - but don't block saving if it's visible
    // This is a more permissive approach that allows saving even when the page is in background
    // but warns the user that they should bring the tab to the foreground for best results
    if (!isPageVisible) {
      toast.warning('For best results, please bring this tab to the foreground before saving.');
      // Continue with saving anyway - don't block the operation
    }

    // Start loading operation
    const loadingId = loadingMonitor.startLoading('saveProject', `Saving project data (step ${currentStep})`);

    setSaving(true);

    try {
      // Use the effective project ID (from either the edit route or the wizard route)
      let projectId = projectIdFromPath || id;

      // Update wizard progress
      const wizardProgress = {
        current_step: moveToNext ? currentStep + 1 : currentStep,
        completed_steps: [...new Set([...completedSteps, currentStep])]
      };

      // Retry logic for saving project
      const saveWithRetry = async (operation, maxRetries = 3) => {
        let retryCount = 0;
        let result = null;
        let lastError = null;

        while (!result && retryCount < maxRetries) {
          try {
            result = await operation();
            return result;
          } catch (err) {
            console.warn(`Save attempt ${retryCount + 1} failed:`, err);
            lastError = err;
            retryCount++;
            if (retryCount < maxRetries) {
              // Wait before retrying (exponential backoff)
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }

        if (!result) {
          throw lastError || new Error('Operation failed after multiple attempts');
        }
      };

      if (!projectId || projectId === 'new') {
        // Create new project
        const createProject = async () => {
          try {
            // Try with both name and title fields to handle different database schemas
            const { data: newProject, error: projectError } = await supabase
              .from('projects')
              .insert([{
                id: crypto.randomUUID(), // Generate a UUID for the project
                name: projectData.name,
                title: projectData.name, // Include both fields to be safe
                description: projectData.description,
                created_by: currentUser.id,
                project_type: projectData.project_type,
                thumbnail_url: projectData.thumbnail_url,
                start_date: projectData.start_date,
                launch_date: projectData.launch_date,
                estimated_duration: projectData.estimated_duration,
                is_public: projectData.is_public,

                // Company Information
                company_name: projectData.company_name,
                company_address: projectData.company_address,
                company_state: projectData.company_state,
                company_county: projectData.company_county,
                company_city: projectData.company_city,
                contact_email: projectData.contact_email,
                signer_name: projectData.signer_name,
                signer_title: projectData.signer_title,

                // Project-Specific Fields
                engine: projectData.engine,
                platforms: projectData.platforms,
                genre: projectData.genre,
                technology_stack: projectData.technology_stack,
                distribution_platforms: projectData.distribution_platforms,

                wizard_progress: wizardProgress
              }])
              .select()
              .single();

            if (projectError) throw projectError;
            return newProject;
          } catch (error) {
            console.error('Error in createProject:', error);
            throw error;
          }
        };

        const newProject = await saveWithRetry(createProject);
        projectId = newProject.id;

        // Add current user as owner
        const addOwner = async () => {
          const { error: contributorError } = await supabase
            .from('project_contributors')
            .insert([{
              project_id: projectId,
              user_id: currentUser.id,
              email: currentUser.email,
              display_name: currentUser.user_metadata?.full_name || currentUser.email,
              role: 'Owner',
              permission_level: 'Owner',
              is_admin: true,
              status: 'active',
              joined_at: new Date().toISOString()
            }]);

          if (contributorError) throw contributorError;
          return true;
        };

        await saveWithRetry(addOwner);
      } else {
        // Update existing project
        const updateProject = async () => {
          try {
            // First try with 'name' field (as per database schema)
            const { error: projectError } = await supabase
              .from('projects')
              .update({
                name: projectData.name,
                description: projectData.description,
                project_type: projectData.project_type,
                thumbnail_url: projectData.thumbnail_url,
                start_date: projectData.start_date,
                launch_date: projectData.launch_date,
                estimated_duration: projectData.estimated_duration,
                is_public: projectData.is_public,
                wizard_progress: wizardProgress
              })
              .eq('id', projectId);

            if (projectError) {
              // If that fails, try with 'title' field (in case schema was changed)
              console.log('Trying with title field instead of name');
              const { error: titleError } = await supabase
                .from('projects')
                .update({
                  title: projectData.name,
                  description: projectData.description,
                  project_type: projectData.project_type,
                  thumbnail_url: projectData.thumbnail_url,
                  start_date: projectData.start_date,
                  launch_date: projectData.launch_date,
                  estimated_duration: projectData.estimated_duration,
                  is_public: projectData.is_public,
                  wizard_progress: wizardProgress
                })
                .eq('id', projectId);

              if (titleError) throw titleError;
            }
            return true;
          } catch (error) {
            console.error('Error in updateProject:', error);
            throw error;
          }
        };

        await saveWithRetry(updateProject);
      }

      // Save step-specific data
      if (currentStep === 2) {
        // Save contributors
        for (const contributor of projectData.contributors) {
          if (contributor.id) {
            // Update existing contributor
            const updateContributor = async () => {
              const { error: contributorError } = await supabase
                .from('project_contributors')
                .update({
                  role: contributor.role,
                  permission_level: contributor.permission_level,
                  is_admin: contributor.is_admin,
                  updated_at: new Date().toISOString()
                })
                .eq('id', contributor.id);

              if (contributorError) throw contributorError;
              return true;
            };

            await saveWithRetry(updateContributor);
          } else {
            // Add new contributor
            const addContributor = async () => {
              const { error: contributorError } = await supabase
                .from('project_contributors')
                .insert([{
                  project_id: projectId,
                  user_id: contributor.user_id,
                  email: contributor.email,
                  display_name: contributor.display_name,
                  role: contributor.role,
                  permission_level: contributor.permission_level,
                  is_admin: contributor.is_admin
                }]);

              if (contributorError) throw contributorError;
              return true;
            };

            await saveWithRetry(addContributor);
          }
        }
      } else if (currentStep === 3) {
        // Save royalty model
        const checkModel = async () => {
          const { data, error } = await supabase
            .from('royalty_models')
            .select('id')
            .eq('project_id', projectId);

          if (error) throw error;
          return data;
        };

        const existingModel = await saveWithRetry(checkModel);

        if (existingModel && existingModel.length > 0) {
          // Update existing model
          const updateModel = async () => {
            try {
              // First check if model_schema column exists
              const { error: checkError } = await supabase.rpc('check_column_exists', {
                table_name: 'royalty_models',
                column_name: 'model_schema'
              });

              if (checkError) {
                console.warn('Could not check if model_schema column exists:', checkError);
                // Fall back to updating without model_schema
                const { error: modelError } = await supabase
                  .from('royalty_models')
                  .update({
                    model_type: projectData.royalty_model.model_type,
                    configuration: projectData.royalty_model.configuration,
                    is_pre_expense: projectData.royalty_model.is_pre_expense,
                    updated_at: new Date().toISOString()
                  })
                  .eq('project_id', projectId);

                if (modelError) throw modelError;
              } else {
                // Update with model_schema
                const { error: modelError } = await supabase
                  .from('royalty_models')
                  .update({
                    model_type: projectData.royalty_model.model_type,
                    model_schema: projectData.royalty_model.model_schema || 'cog',
                    configuration: projectData.royalty_model.configuration,
                    is_pre_expense: projectData.royalty_model.is_pre_expense,
                    // Platform fee percentage removed as it's handled by platforms directly
                    updated_at: new Date().toISOString()
                  })
                  .eq('project_id', projectId);

                if (modelError) throw modelError;
              }
              return true;
            } catch (error) {
              console.error('Error updating royalty model:', error);
              // Try a fallback approach with minimal fields
              const { error: modelError } = await supabase
                .from('royalty_models')
                .update({
                  model_type: projectData.royalty_model.model_type,
                  configuration: projectData.royalty_model.configuration,
                  is_pre_expense: projectData.royalty_model.is_pre_expense,
                  updated_at: new Date().toISOString()
                })
                .eq('project_id', projectId);

              if (modelError) throw modelError;
              return true;
            }
          };

          await saveWithRetry(updateModel);
        } else {
          // Create new model
          const createModel = async () => {
            try {
              // First check if model_schema column exists
              const { error: checkError } = await supabase.rpc('check_column_exists', {
                table_name: 'royalty_models',
                column_name: 'model_schema'
              });

              if (checkError) {
                console.warn('Could not check if model_schema column exists:', checkError);
                // Fall back to inserting without model_schema
                const { error: modelError } = await supabase
                  .from('royalty_models')
                  .insert([{
                    project_id: projectId,
                    model_type: projectData.royalty_model.model_type,
                    configuration: projectData.royalty_model.configuration,
                    is_pre_expense: projectData.royalty_model.is_pre_expense
                    // Platform fee percentage removed as it's handled by platforms directly
                  }]);

                if (modelError) throw modelError;
              } else {
                // Insert with model_schema
                const { error: modelError } = await supabase
                  .from('royalty_models')
                  .insert([{
                    project_id: projectId,
                    model_type: projectData.royalty_model.model_type,
                    model_schema: projectData.royalty_model.model_schema || 'cog',
                    configuration: projectData.royalty_model.configuration,
                    is_pre_expense: projectData.royalty_model.is_pre_expense
                    // Platform fee percentage removed as it's handled by platforms directly
                  }]);

                if (modelError) throw modelError;
              }
              return true;
            } catch (error) {
              console.error('Error creating royalty model:', error);
              // Try a fallback approach without the new fields
              const { error: modelError } = await supabase
                .from('royalty_models')
                .insert([{
                  project_id: projectId,
                  model_type: projectData.royalty_model.model_type,
                  configuration: projectData.royalty_model.configuration,
                  is_pre_expense: projectData.royalty_model.is_pre_expense
                }]);

              if (modelError) throw modelError;
              return true;
            }
          };

          await saveWithRetry(createModel);
        }
      } else if (currentStep === 4) {
        // Save revenue tranches
        console.log('💾 Saving revenue tranches:', projectData.revenue_tranches);

        // Validate revenue tranches data
        if (!projectData.revenue_tranches || projectData.revenue_tranches.length === 0) {
          console.log('⚠️ No revenue tranches to save, skipping step 4 save');
          return { success: true, projectId };
        }

        // First, delete existing tranches
        const deleteTranches = async () => {
          console.log('🗑️ Deleting existing revenue tranches for project:', projectId);
          const { error } = await supabase
            .from('revenue_tranches')
            .delete()
            .eq('project_id', projectId);

          if (error) {
            console.error('❌ Error deleting revenue tranches:', error);
            throw error;
          }
          console.log('✅ Successfully deleted existing revenue tranches');
          return true;
        };

        await saveWithRetry(deleteTranches);

        // Then insert new tranches
        for (let i = 0; i < projectData.revenue_tranches.length; i++) {
          const tranche = projectData.revenue_tranches[i];
          console.log(`💾 Saving tranche ${i + 1}:`, tranche);

          const createTranche = async () => {
            // Ensure all required fields are present and properly formatted
            const trancheData = {
              project_id: projectId,
              name: tranche.name || 'Unnamed Tranche',
              description: tranche.description || '',
              start_date: tranche.start_date || null,
              end_date: tranche.end_date || null,
              revenue_sources: tranche.revenue_sources || [],
              platform_fee_config: tranche.platform_fee_config || {
                apply_before: true,
                percentage: 5
              },
              distribution_thresholds: tranche.distribution_thresholds || {
                minimum_revenue: 0,
                maximum_payout: null,
                per_contributor_minimum: 0,
                per_contributor_maximum: null
              },
              rollover_config: tranche.rollover_config || 'none'
            };

            console.log('📤 Inserting tranche data:', trancheData);

            const { error, data } = await supabase
              .from('revenue_tranches')
              .insert([trancheData])
              .select();

            if (error) {
              console.error('❌ Error creating revenue tranche:', error);
              throw error;
            }

            console.log('✅ Successfully created revenue tranche:', data);
            return true;
          };

          await saveWithRetry(createTranche);
        }

        console.log('🎉 All revenue tranches saved successfully');
      } else if (currentStep === 6) {
        // Save milestones
        // First, delete existing milestones
        const deleteMilestones = async () => {
          const { error } = await supabase
            .from('milestones')
            .delete()
            .eq('project_id', projectId);

          if (error) throw error;
          return true;
        };

        await saveWithRetry(deleteMilestones);

        // Then insert new milestones
        for (const milestone of projectData.milestones) {
          const createMilestone = async () => {
            const { error } = await supabase
              .from('milestones')
              .insert([{
                project_id: projectId,
                name: milestone.name,
                description: milestone.description,
                deadline: milestone.deadline,
                deliverables: milestone.deliverables,
                approval_criteria: milestone.approval_criteria
              }]);

            if (error) throw error;
            return true;
          };

          await saveWithRetry(createMilestone);
        }
      }

      toast.success('Project saved successfully');

      // Update local state
      if (moveToNext) {
        setCurrentStep(currentStep + 1);
        setCompletedSteps([...new Set([...completedSteps, currentStep])]);
      }

      // If this is a new project and we're not moving to next step, redirect to the wizard URL
      if (!id && !projectIdFromPath && !moveToNext) {
        navigate(`/project/wizard/${projectId}`);
      }

      // If we're on the edit route, redirect back to the project detail page after saving
      if (isEditRoute && !moveToNext) {
        navigate(`/project/${projectId}`);
      }

      // End loading operation with success
      loadingMonitor.endLoading(loadingId, 'success');
      return { success: true, projectId }; // Return success and project ID
    } catch (error) {
      console.error('Error saving project:', error);
      toast.error('Failed to save project. Please try again.');
      // End loading operation with error
      loadingMonitor.endLoading(loadingId, `error: ${error.message}`);
      return { success: false, projectId: null }; // Indicate failure
    } finally {
      setSaving(false);
    }
  };

  // Handle next step
  const handleNext = async () => {
    // Handle studio selection step
    if (!projectData.team_id) {
      if (userStudios.length > 0) {
        toast.error('Please select a studio for your project');
        return;
      } else {
        toast.error('Please create a studio first');
        return;
      }
    }

    // Validate current step
    let isValid = true;

    if (currentStep === 1) {
      // Validate project basics
      if (!projectData.name) {
        toast.error('Project name is required');
        isValid = false;
      }
    }

    if (isValid) {
      const result = await saveProject(true);
      if (result && result.success) {
        // Update URL to reflect new step
        const newStep = currentStep + 1;
        const projectId = result.projectId || projectIdFromPath || id;
        if (projectId) {
          const basePath = isEditRoute ? `/project/${projectId}/edit` : `/project/wizard/${projectId}`;
          navigate(`${basePath}/step/${newStep}`, { replace: true });
        }
      }
    }
  };

  // Handle previous step
  const handlePrevious = async () => {
    if (currentStep > 1) {
      // Save current progress before going back
      await saveProject(false);
      const newStep = currentStep - 1;
      setCurrentStep(newStep);

      // Update URL to reflect new step
      const projectId = projectIdFromPath || id;
      if (projectId) {
        const basePath = isEditRoute ? `/project/${projectId}/edit` : `/project/wizard/${projectId}`;
        navigate(`${basePath}/step/${newStep}`, { replace: true });
      }
    }
  };

  // Handle save
  const handleSave = async () => {
    await saveProject(false);
  };

  // Handle finish
  const handleFinish = async () => {
    const result = await saveProject(false);
    if (result && result.success) {
      // Trigger data sync for project creation/update
      triggerProjectSync();

      toast.success('Project completed successfully!');

      // Get the project ID for navigation
      const projectId = result.projectId || projectIdFromPath || id;

      // Navigate to project dashboard instead of home
      if (projectId) {
        setTimeout(() => navigate(`/project/${projectId}`), 1500);
      } else {
        // Fallback to projects list if no project ID
        setTimeout(() => navigate('/projects'), 1500);
      }
    }
  };

  // Handle studio selection
  const handleStudioSelect = (studioId) => {
    const selectedStudio = userStudios.find(studio => studio.id === studioId);
    setProjectData({ ...projectData, team_id: studioId });
    if (selectedStudio) {
      toast.success(`Studio "${selectedStudio.name}" selected. Let's set up your project!`, {
        duration: 2000
      });
    }
  };

  const handleStudioCreated = (studio) => {
    setUserStudios([...userStudios, studio]);
    setProjectData({ ...projectData, team_id: studio.id });
    setShowStudioCreation(false);
    // Move to next step after studio creation
    setCurrentStep(1);
    setCompletedSteps([0]);
    // Additional feedback is already provided in the StudioSelectionStep component
  };

  // Render current step
  const renderStep = () => {
    // Show studio selection if user needs to select/create a studio
    if (!studioCheckComplete) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-default-600">Loading your studios...</p>
          </div>
        </div>
      );
    }

    // Show studio selection step if no studio is selected or user wants to change studio
    if (!projectData.team_id || showStudioCreation) {
      return (
        <StudioSelectionStep
          userStudios={userStudios}
          selectedStudioId={projectData.team_id}
          onStudioSelect={handleStudioSelect}
          onStudioCreated={handleStudioCreated}
        />
      );
    }

    switch (currentStep) {
      case 1:
        return (
          <ProjectBasics
            projectData={projectData}
            setProjectData={setProjectData}
          />
        );
      case 2:
        return (
          <TeamContributors
            projectData={projectData}
            setProjectData={setProjectData}
            projectId={projectIdFromPath || id}
          />
        );
      case 3:
        return (
          <RoyaltyModel
            projectData={projectData}
            setProjectData={setProjectData}
          />
        );
      case 4:
        return (
          <RevenueTranches
            projectData={projectData}
            setProjectData={setProjectData}
          />
        );
      case 5:
        return (
          <ContributionTracking
            projectData={projectData}
            setProjectData={setProjectData}
          />
        );
      case 6:
        return (
          <Milestones
            projectData={projectData}
            setProjectData={setProjectData}
          />
        );
      case 7:
        return (
          <ReviewAgreement
            projectData={projectData}
            setProjectData={setProjectData}
            projectId={projectIdFromPath || id}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  // Enhanced Project Wizard is now the default and only option
  // The wizard that was previously called "traditional" is now the Enhanced Project Wizard

  // Enhanced Project Wizard - the main and only wizard interface
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-1 px-3">
        <div className="max-w-6xl mx-auto">
          {/* Project Wizard Header - Compact */}
          {(!id && !projectIdFromPath) || isCreateRoute ? (
            <div className="text-center mb-2">
              <h1 className="text-xl md:text-2xl font-bold text-primary mb-1">🚀 Create Your Project</h1>
              <p className="text-default-600 text-sm md:text-base max-w-xl mx-auto">
                Follow our guided wizard to set up your project with smart defaults and best practices.
              </p>
            </div>
          ) : null}

          {id && (
            <div className="text-center mb-2">
              <h1 className="text-xl md:text-2xl font-bold text-foreground mb-1">
                Edit Project
              </h1>
              <p className="text-default-600 text-sm">
                Update your project settings and configuration
              </p>
            </div>
          )}

          {/* Wizard Navigation - Compact */}
          <div className="mb-2">
            <WizardNavigation
              currentStep={currentStep}
              completedSteps={completedSteps}
              setCurrentStep={(stepNumber) => {
                setCurrentStep(stepNumber);
                // Update URL when step changes via navigation
                const projectId = projectIdFromPath || id;
                if (projectId) {
                  const basePath = isEditRoute ? `/project/${projectId}/edit` : `/project/wizard/${projectId}`;
                  navigate(`${basePath}/step/${stepNumber}`, { replace: true });
                }
              }}
              totalSteps={7}
            />
          </div>

          {/* Main Wizard Content - Optimized Layout */}
          <Card className="shadow-lg border-0">
            <CardBody className="p-2 md:p-3">
              <div className="wizard-content-container">
                {renderStep()}
              </div>

              {/* Navigation Buttons - Only show for regular wizard steps */}
              {projectData.team_id && !showStudioCreation && (
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6 pt-6 border-t border-divider">
                  <Button
                    type="button"
                    variant="flat"
                    onPress={handlePrevious}
                    disabled={currentStep === 1 || saving}
                    size="lg"
                    className="w-full sm:w-auto"
                  >
                    <i className="bi bi-arrow-left mr-2"></i>
                    Previous
                  </Button>

                  <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <Button
                      type="button"
                      variant="bordered"
                      onPress={handleSave}
                      disabled={saving}
                      size="lg"
                      className="w-full sm:w-auto"
                    >
                      <i className="bi bi-save mr-2"></i>
                      {saving ? 'Saving...' : 'Save Progress'}
                    </Button>

                    {currentStep < 7 ? (
                      <Button
                        type="button"
                        onPress={handleNext}
                        disabled={saving}
                        data-testid="wizard-next-button"
                        size="lg"
                        className="w-full sm:w-auto"
                        color="primary"
                      >
                        {saving ? 'Saving...' : 'Next'}
                        <i className="bi bi-arrow-right ml-2"></i>
                      </Button>
                    ) : (
                      <Button
                        type="button"
                        onPress={handleFinish}
                        disabled={saving}
                        size="lg"
                        className="w-full sm:w-auto bg-success text-success-foreground hover:bg-success/90"
                      >
                        <i className="bi bi-check-circle mr-2"></i>
                        {saving ? 'Saving...' : 'Finish Project'}
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProjectWizard;
