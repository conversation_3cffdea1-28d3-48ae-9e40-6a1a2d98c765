import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import projectTemplates from '../../../data/project-templates';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Accordion,
  AccordionItem,
  Switch,
  Divider,
  Chip
} from '../../ui/heroui';
import { Building, FileText, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

const ProjectBasics = ({ projectData, setProjectData }) => {
  const [uploading, setUploading] = useState(false);
  const [studioLegalInfo, setStudioLegalInfo] = useState(null);
  const [useStudioInfo, setUseStudioInfo] = useState(true);
  const [loadingStudioInfo, setLoadingStudioInfo] = useState(false);

  // Project types
  const projectTypes = [
    { value: 'game', label: 'Game' },
    { value: 'app', label: 'App' },
    { value: 'website', label: 'Website' },
    { value: 'plugin', label: 'Plugin/Extension' },
    { value: 'art', label: 'Art/Asset Pack' },
    { value: 'music', label: 'Music/Sound' },
    { value: 'other', label: 'Other' }
  ];

  // State for template selection
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  // Fetch studio legal information when team_id changes
  useEffect(() => {
    if (projectData.team_id && useStudioInfo) {
      fetchStudioLegalInfo();
    }
  }, [projectData.team_id, useStudioInfo]);

  // Fetch studio legal information
  const fetchStudioLegalInfo = async () => {
    if (!projectData.team_id) return;

    setLoadingStudioInfo(true);
    try {
      const { data: studio, error } = await supabase
        .from('teams')
        .select('*')
        .eq('id', projectData.team_id)
        .single();

      if (error) throw error;

      if (studio) {
        setStudioLegalInfo(studio);

        // Auto-populate project legal fields from studio if useStudioInfo is true
        if (useStudioInfo) {
          populateFromStudio(studio);
        }
      }
    } catch (error) {
      console.error('Error fetching studio legal info:', error);
      toast.error('Failed to load studio information');
    } finally {
      setLoadingStudioInfo(false);
    }
  };

  // Populate project legal fields from studio information
  const populateFromStudio = (studio) => {
    const updates = {};

    // Basic company information
    if (studio.legal_entity_info?.legal_name) {
      updates.company_name = studio.legal_entity_info.legal_name;
    } else if (studio.name) {
      updates.company_name = studio.name;
    }

    // Contact information
    if (studio.contact_information?.primary_email) {
      updates.company_email = studio.contact_information.primary_email;
    }

    // Address information
    if (studio.business_address) {
      updates.company_address = studio.business_address.address || '';
      updates.company_city = studio.business_address.city || '';
      updates.company_state = studio.business_address.state || '';
      updates.company_county = studio.business_address.county || '';
    }

    // Signatory information
    if (studio.contact_information?.signer_name) {
      updates.signer_name = studio.contact_information.signer_name;
    }
    if (studio.contact_information?.signer_title) {
      updates.signer_title = studio.contact_information.signer_title;
    }

    // Apply updates
    setProjectData(prev => ({ ...prev, ...updates }));

    toast.success('Legal information populated from studio');
  };

  // Toggle studio information usage
  const toggleStudioInfo = (enabled) => {
    setUseStudioInfo(enabled);

    if (enabled && studioLegalInfo) {
      populateFromStudio(studioLegalInfo);
    } else if (!enabled) {
      // Clear auto-populated fields when disabled
      const clearedFields = {
        company_name: '',
        company_email: '',
        company_address: '',
        company_city: '',
        company_state: '',
        company_county: '',
        signer_name: '',
        signer_title: ''
      };
      setProjectData(prev => ({ ...prev, ...clearedFields }));
      toast.info('Legal fields cleared - enter project-specific information');
    }
  };
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Apply template when project type changes
  useEffect(() => {
    // Only suggest template if this is a new project (name is empty)
    if (projectData.project_type && !projectData.name) {
      setShowTemplateModal(true);
    }
  }, [projectData.project_type]);

  // Apply selected template
  const applyTemplate = (templateKey) => {
    const template = projectTemplates[templateKey];
    if (!template) {
      toast.error('Template not found');
      return;
    }

    // Keep the current project type and any existing data
    const updatedData = {
      ...template,
      project_type: projectData.project_type,
      // Preserve any existing data that shouldn't be overwritten
      thumbnail_url: projectData.thumbnail_url || template.thumbnail_url || '',
      start_date: projectData.start_date || template.start_date || new Date(),
      launch_date: projectData.launch_date || template.launch_date || null
    };

    setProjectData(updatedData);
    setShowTemplateModal(false);
    toast.success(`Applied ${templateKey} template`);
  };

  // Skip template
  const skipTemplate = () => {
    setShowTemplateModal(false);
    toast.info('Template skipped');
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const fileExt = file.name.split('.').pop();
    const allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    if (!allowedExts.includes(fileExt.toLowerCase())) {
      toast.error('Invalid file type. Please upload an image file.');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size too large. Maximum size is 2MB.');
      return;
    }

    setUploading(true);
    const uploadToastId = toast.loading('Uploading thumbnail...');

    try {
      // Create a unique file name
      const fileName = `project-${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `project-thumbnails/${fileName}`;

      // Use the avatars bucket
      const bucketName = 'avatars';

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        // If the error is because the bucket doesn't exist, try to create it
        if (uploadError.message.includes('bucket') && uploadError.message.includes('not found')) {
          toast.error('Storage bucket not found. Please contact an administrator.', { id: uploadToastId });
          setUploading(false);
          return;
        }
        throw new Error(`Upload error: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData || !urlData.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      // Update project data
      setProjectData({
        ...projectData,
        thumbnail_url: urlData.publicUrl
      });

      toast.success('Thumbnail uploaded successfully', { id: uploadToastId });
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      toast.error(`Failed to upload thumbnail: ${error.message}`, { id: uploadToastId });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="wizard-step-content">
      {/* Step Header */}
      <div className="mb-6">
        <h2 className="wizard-heading-responsive text-foreground mb-2">Project Basics</h2>
        <p className="wizard-text-responsive text-default-600">
          Let's start with the basic information about your project. This will help us set up the foundation for your collaboration.
        </p>
      </div>

      {/* Template Selection Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-lg mx-4 shadow-2xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">Use Project Template?</h3>
                <Button
                  variant="light"
                  size="sm"
                  onClick={skipTemplate}
                  aria-label="Close"
                  isIconOnly
                >
                  <i className="bi bi-x-lg"></i>
                </Button>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <p className="text-default-600">
                Would you like to use a template for your {projectData.project_type} project?
                This will pre-populate settings, milestones, and contribution tracking.
              </p>

              <div className="p-4 bg-default-100 rounded-lg">
                <h4 className="font-semibold mb-3 text-foreground">Template includes:</h4>
                <ul className="text-sm text-default-600 space-y-2">
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Pre-configured royalty model
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Relevant contribution categories and task types
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Standard milestones for {projectData.project_type} projects
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Recommended revenue tranches
                  </li>
                </ul>
              </div>

              <div className="flex gap-3 pt-2">
                <Button
                  onClick={() => applyTemplate(projectData.project_type)}
                  className="flex-1"
                  color="primary"
                  size="lg"
                >
                  <i className="bi bi-magic mr-2"></i>
                  Use Template
                </Button>
                <Button
                  variant="bordered"
                  onClick={skipTemplate}
                  className="flex-1"
                  size="lg"
                >
                  Start from Scratch
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Main Content - Compact Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Left Column - Main Form Fields */}
        <div className="lg:col-span-2 space-y-4">
          {/* Basic Project Information */}
          <Card className="shadow-sm">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <i className="bi bi-info-circle text-primary text-lg"></i>
                <h3 className="text-lg font-semibold">Basic Information</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0 space-y-3">
              <Input
                label="Project Name"
                placeholder="Enter a clear, descriptive project name"
                value={projectData.name || ""}
                onValueChange={(value) => setProjectData({ ...projectData, name: value })}
                isRequired
                description="This will be the main identifier for your project"
                size="md"
                variant="bordered"
                classNames={{
                  input: "text-base",
                  inputWrapper: "h-12"
                }}
              />

              <Textarea
                label="Project Description"
                placeholder="Describe what your project is about, its goals, and what makes it unique"
                value={projectData.description || ""}
                onValueChange={(value) => setProjectData({ ...projectData, description: value })}
                minRows={3}
                description="A clear description helps potential contributors understand your vision"
                variant="bordered"
                classNames={{
                  input: "text-sm"
                }}
              />
            </CardBody>
          </Card>

          {/* Project Type & Configuration */}
          <Card className="shadow-sm">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <i className="bi bi-gear text-primary text-lg"></i>
                <h3 className="text-lg font-semibold">Project Configuration</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0 space-y-3">
              <Select
                label="Project Type"
                placeholder="Select your project type"
                selectedKeys={projectData.project_type ? [projectData.project_type] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  setProjectData({ ...projectData, project_type: selectedKey });
                }}
                description="Choose the type that best describes your project"
                size="md"
                variant="bordered"
                classNames={{
                  trigger: "h-12"
                }}
              >
                {projectTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </Select>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Input
                  label="Estimated Duration (months)"
                  type="number"
                  min="1"
                  max="60"
                  value={projectData.estimated_duration?.toString() || "6"}
                  onValueChange={(value) => setProjectData({ ...projectData, estimated_duration: parseInt(value) || 6 })}
                  description="How long do you expect this project to take?"
                  size="md"
                  variant="bordered"
                  classNames={{
                    inputWrapper: "h-12"
                  }}
                />

                <Input
                  label="Launch Date"
                  type="date"
                  value={projectData.launch_date ? new Date(projectData.launch_date).toISOString().split('T')[0] : ""}
                  onValueChange={(value) => {
                    const date = value ? new Date(value) : null;
                    setProjectData({ ...projectData, launch_date: date });
                  }}
                  description="When do you plan to launch? (Optional)"
                  size="md"
                  variant="bordered"
                  classNames={{
                    inputWrapper: "h-12"
                  }}
                />
              </div>
            </CardBody>
          </Card>

          {/* Legal Entity Information Section */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <Building className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="studio-creation-section-title">Legal Entity Information</h3>
                      <p className="text-sm text-default-500">Required for comprehensive legal agreement generation</p>
                    </div>

                    {/* Studio Integration Toggle */}
                    {projectData.team_id && (
                      <div className="flex items-center gap-3">
                        {loadingStudioInfo && <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />}
                        <Switch
                          isSelected={useStudioInfo}
                          onValueChange={toggleStudioInfo}
                          size="sm"
                          color="primary"
                        >
                          <span className="text-sm">Use Studio Info</span>
                        </Switch>
                      </div>
                    )}
                  </div>

                  {/* Studio Info Status */}
                  {projectData.team_id && studioLegalInfo && (
                    <div className="mt-2">
                      <Chip
                        size="sm"
                        color={useStudioInfo ? "success" : "default"}
                        variant="flat"
                        startContent={useStudioInfo ? <CheckCircle className="w-3 h-3" /> : <AlertCircle className="w-3 h-3" />}
                      >
                        {useStudioInfo ? `Using ${studioLegalInfo.name} studio information` : 'Using project-specific information'}
                      </Chip>
                    </div>
                  )}
                </div>
              </div>

              <div className="wizard-space-y">
                {/* Legal Compliance Status */}
                {studioLegalInfo && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="font-semibold text-blue-900 mb-2">Legal Compliance Status</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            {studioLegalInfo.legal_entity_info?.business_type ?
                              <CheckCircle className="w-4 h-4 text-green-600" /> :
                              <AlertCircle className="w-4 h-4 text-orange-500" />
                            }
                            <span>Business Type: {studioLegalInfo.legal_entity_info?.business_type || 'Not specified'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {studioLegalInfo.legal_entity_info?.tax_id ?
                              <CheckCircle className="w-4 h-4 text-green-600" /> :
                              <AlertCircle className="w-4 h-4 text-orange-500" />
                            }
                            <span>Tax ID: {studioLegalInfo.legal_entity_info?.tax_id ? 'Provided' : 'Not provided'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {studioLegalInfo.business_address?.address ?
                              <CheckCircle className="w-4 h-4 text-green-600" /> :
                              <AlertCircle className="w-4 h-4 text-orange-500" />
                            }
                            <span>Address: {studioLegalInfo.business_address?.address ? 'Complete' : 'Incomplete'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {studioLegalInfo.contact_information?.signer_name ?
                              <CheckCircle className="w-4 h-4 text-green-600" /> :
                              <AlertCircle className="w-4 h-4 text-orange-500" />
                            }
                            <span>Signatory: {studioLegalInfo.contact_information?.signer_name ? 'Designated' : 'Not designated'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Company Information Fields */}
                <div className="wizard-grid wizard-grid-2">
                  <div className="wizard-form-field">
                    <Input
                      label="Legal Company Name"
                      value={projectData.company_name || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_name: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "Enter legal company name"}
                      description="Official registered name of the entity owning this project"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="Primary Contact Email"
                      type="email"
                      value={projectData.company_email || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_email: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "<EMAIL>"}
                      description="Primary email for legal communications"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div className="wizard-form-field">
                  <Input
                    label="Business Address"
                    value={projectData.company_address || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_address: value })}
                    placeholder={useStudioInfo ? "Auto-populated from studio" : "123 Business Street, Suite 100"}
                    description="Complete street address for legal documentation"
                    isRequired
                    size="lg"
                    variant="bordered"
                    isDisabled={useStudioInfo && loadingStudioInfo}
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="wizard-grid wizard-grid-3">
                  <div className="wizard-form-field">
                    <Input
                      label="City"
                      value={projectData.company_city || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_city: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "San Francisco"}
                      description="City for legal jurisdiction"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="State/Province"
                      value={projectData.company_state || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_state: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "CA"}
                      description="State for legal jurisdiction"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="County/Region"
                      value={projectData.company_county || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_county: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "San Francisco County"}
                      description="County for legal documentation"
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                <Divider className="my-6" />

                {/* Authorized Signatory Section */}
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center gap-2 mb-3">
                    <FileText className="w-5 h-5 text-amber-600" />
                    <h4 className="font-semibold text-amber-900">Authorized Signatory</h4>
                  </div>
                  <p className="text-sm text-amber-700">
                    This person will have the authority to sign legal agreements on behalf of the {useStudioInfo && studioLegalInfo?.legal_entity_info?.business_type === 'individual' ? 'individual' : 'company'}.
                  </p>
                </div>

                <div className="wizard-grid wizard-grid-2">
                  <div className="wizard-form-field">
                    <Input
                      label="Authorized Signer Name"
                      value={projectData.signer_name || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, signer_name: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "John Smith"}
                      description="Person authorized to sign legal agreements"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="Signer Title/Position"
                      value={projectData.signer_title || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, signer_title: value })}
                      placeholder={useStudioInfo ? "Auto-populated from studio" : "CEO, Founder, Owner"}
                      description="Official title of the authorized signer"
                      isRequired
                      size="lg"
                      variant="bordered"
                      isDisabled={useStudioInfo && loadingStudioInfo}
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                {/* Legal Compliance Summary */}
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h4 className="font-semibold text-green-900">Legal Compliance Ready</h4>
                  </div>
                  <p className="text-sm text-green-700">
                    This information will be used to generate comprehensive legal agreements that comply with business entity requirements and provide proper legal protection for all project participants.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Thumbnail & Privacy */}
        <div className="space-y-4">
          {/* Project Thumbnail */}
          <Card className="shadow-sm">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <i className="bi bi-image text-primary text-lg"></i>
                <h3 className="text-lg font-semibold">Project Thumbnail</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              {projectData.thumbnail_url ? (
                <div className="text-center space-y-2">
                  <img
                    src={projectData.thumbnail_url}
                    alt="Project thumbnail"
                    className="w-full h-32 object-cover rounded-lg border border-default-200"
                  />
                  <Button
                    variant="flat"
                    color="danger"
                    size="sm"
                    onClick={() => setProjectData({ ...projectData, thumbnail_url: '' })}
                  >
                    Remove Thumbnail
                  </Button>
                </div>
              ) : (
                <div className="text-center space-y-2">
                  <div className="w-full h-32 bg-default-100 rounded-lg border-2 border-dashed border-default-300 flex items-center justify-center">
                    <div className="text-center">
                      <i className="bi bi-image text-2xl text-default-400 mb-1"></i>
                      <p className="text-xs text-default-500">No thumbnail uploaded</p>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleThumbnailUpload}
                    className="hidden"
                    id="thumbnail-upload"
                    disabled={uploading}
                  />
                  <label htmlFor="thumbnail-upload">
                    <Button
                      variant="bordered"
                      size="sm"
                      as="span"
                      disabled={uploading}
                      className="cursor-pointer"
                    >
                      {uploading ? 'Uploading...' : 'Upload Thumbnail'}
                    </Button>
                  </label>
                </div>
              )}
              <p className="text-xs text-default-500 text-center mt-2">
                Upload a thumbnail image for your project (max 2MB)
              </p>
            </CardBody>
          </Card>

          {/* Project Privacy */}
          <Card className="shadow-sm">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <i className="bi bi-shield-check text-primary text-lg"></i>
                <h3 className="text-lg font-semibold">Project Privacy</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0 space-y-3">
              <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                <div className="flex items-center">
                  {projectData.is_public ? (
                    <i className="bi bi-globe text-success text-lg mr-2"></i>
                  ) : (
                    <i className="bi bi-lock text-warning text-lg mr-2"></i>
                  )}
                  <div>
                    <p className="font-medium text-sm">
                      {projectData.is_public ? 'Public Project' : 'Private Project'}
                    </p>
                    <p className="text-xs text-default-500">
                      {projectData.is_public
                        ? 'Anyone can discover and view your project'
                        : 'Only invited members can see your project'
                      }
                    </p>
                  </div>
                </div>
                <Switch
                  isSelected={projectData.is_public}
                  onValueChange={(value) => setProjectData({ ...projectData, is_public: value })}
                  color="primary"
                  size="md"
                />
              </div>

              {projectData.is_public && (
                <div className="p-3 bg-primary-50 rounded-lg border border-primary-200">
                  <div className="flex items-start">
                    <i className="bi bi-info-circle text-primary text-sm mr-2 mt-0.5"></i>
                    <div>
                      <p className="font-medium text-primary text-sm mb-1">Public Project Benefits</p>
                      <ul className="text-xs text-primary-700 space-y-0.5">
                        <li>• Attract more contributors</li>
                        <li>• Showcase your work</li>
                        <li>• Build community interest</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProjectBasics;
